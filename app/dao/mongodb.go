package dao

import (
	"context"
	"fmt"
	"game.flash.cn/app/app/conf"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"time"
)

type MongoStore struct {
	client  *mongo.Client
	db      *mongo.Database
	timeout time.Duration
}

func NewMongoStore(client *mongo.Client, database string
, timeout time.Duration) *MongoStore {
	return &MongoStore{
		client:  client,
		db:      client.Database(database),
		timeout: timeout * time.Second,
	}
}

func (m *MongoStore) DB() *mongo.Database {
	return m.db
}

func (m *MongoStore) InsertOne(collectionName string, data interface{}) (*mongo.InsertOneResult, error) {
	return m.db.Collection(collectionName).InsertOne(context.TODO(), data)
}

func (m *MongoStore) Client() *mongo.Client {
	return m.client
}

func NewMongoClient(conf *conf.MongoDB) (*mongo.Client, error) {
	return newMongoClient(conf)
}

func getMongoConnectUri(mongoConf *conf.MongoDB) string {
	return fmt.Sprintf("mongodb://%s:%s@%s/%s", mongoConf.User, mongoConf.Password, mongoConf.Host, mongoConf.Database)
}

func newMongoClient(conf *conf.MongoDB) (*mongo.Client, error) {
	client, err := mongo.NewClient(options.Client().ApplyURI(getMongoConnectUri(conf)).SetMaxPoolSize(uint64(conf.MaxPoolSize)))
	if err != nil {
		return nil, err
	}
	err = client.Connect(context.Background())
	if err != nil {
		return nil, err
	}
	ctxTimeout, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	err = client.Ping(ctxTimeout, nil)
	if err != nil {
		return nil, err
	}

	return client, nil
}
